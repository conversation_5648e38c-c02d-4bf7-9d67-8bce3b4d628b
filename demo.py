#!/usr/bin/env python3
"""
LLASA 3B TTS Demo Script
Simple command-line demo for testing the TTS functionality.
"""

import argparse
import sys
import os
from pathlib import Path

# Add current directory to Python path
sys.path.append(str(Path(__file__).parent))

from llasa_tts import LLASATTSModel
from audio_utils import validate_audio_upload

def main():
    parser = argparse.ArgumentParser(description="LLASA 3B TTS Demo")
    parser.add_argument("--voice-sample", required=True, help="Path to voice sample audio file")
    parser.add_argument("--text", required=True, help="Text to synthesize")
    parser.add_argument("--output", default="output.wav", help="Output audio file path")
    parser.add_argument("--prompt-text", help="Optional prompt text (if not provided, will be auto-transcribed)")
    
    args = parser.parse_args()
    
    # Validate voice sample
    print("Validating voice sample...")
    is_valid, message, quality = validate_audio_upload(args.voice_sample)
    
    if not is_valid:
        print(f"❌ Voice sample validation failed: {message}")
        return 1
    
    print(f"✅ Voice sample is valid: {message}")
    print(f"Quality score: {quality.get('overall_score', 0):.1%}")
    
    # Load model
    print("Loading LLASA TTS model...")
    try:
        model = LLASATTSModel()
        print("✅ Model loaded successfully!")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return 1
    
    # Generate speech
    print(f"Generating speech for: '{args.text}'")
    try:
        output_path = model.generate_and_save(
            sample_audio_path=args.voice_sample,
            target_text=args.text,
            output_path=args.output,
            prompt_text=args.prompt_text
        )
        print(f"✅ Speech generated successfully: {output_path}")
        return 0
    except Exception as e:
        print(f"❌ Failed to generate speech: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
