"""
Audio Processing Utilities for LLASA TTS
Handles audio format validation, conversion, and processing.
"""

import numpy as np
import soundfile as sf
import librosa
from typing import <PERSON>ple, Optional, List
import tempfile
import os
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# Supported audio formats
SUPPORTED_FORMATS = ['.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac']

# Audio constraints
MIN_DURATION = 1.0  # seconds
MAX_DURATION = 30.0  # seconds
TARGET_SAMPLE_RATE = 24000
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

class AudioProcessor:
    """Audio processing utilities for TTS applications."""
    
    def __init__(self, target_sr: int = TARGET_SAMPLE_RATE):
        """
        Initialize audio processor.
        
        Args:
            target_sr: Target sample rate for processing
        """
        self.target_sr = target_sr
    
    def validate_audio_file(self, file_path: str) -> Tuple[bool, str]:
        """
        Validate audio file format and constraints.
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            file_path = Path(file_path)
            
            # Check if file exists
            if not file_path.exists():
                return False, "File does not exist"
            
            # Check file size
            file_size = file_path.stat().st_size
            if file_size > MAX_FILE_SIZE:
                return False, f"File too large. Maximum size: {MAX_FILE_SIZE // (1024*1024)}MB"
            
            # Check file extension
            if file_path.suffix.lower() not in SUPPORTED_FORMATS:
                return False, f"Unsupported format. Supported: {', '.join(SUPPORTED_FORMATS)}"
            
            # Try to load and validate audio content
            try:
                audio, sr = librosa.load(str(file_path), sr=None)
                duration = len(audio) / sr
                
                if duration < MIN_DURATION:
                    return False, f"Audio too short. Minimum duration: {MIN_DURATION}s"
                
                if duration > MAX_DURATION:
                    return False, f"Audio too long. Maximum duration: {MAX_DURATION}s"
                
                # Check for valid audio data
                if np.all(audio == 0):
                    return False, "Audio file contains no sound"
                
                return True, "Valid audio file"
                
            except Exception as e:
                return False, f"Cannot read audio file: {str(e)}"
                
        except Exception as e:
            return False, f"File validation error: {str(e)}"
    
    def load_and_preprocess(self, file_path: str) -> Tuple[np.ndarray, int]:
        """
        Load and preprocess audio file.
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Tuple of (audio_array, sample_rate)
        """
        try:
            # Load audio
            audio, sr = librosa.load(file_path, sr=self.target_sr)
            
            # Normalize audio
            audio = librosa.util.normalize(audio)
            
            # Remove silence from beginning and end
            audio, _ = librosa.effects.trim(audio, top_db=20)
            
            # Ensure minimum length
            min_samples = int(MIN_DURATION * self.target_sr)
            if len(audio) < min_samples:
                # Pad with silence
                padding = min_samples - len(audio)
                audio = np.pad(audio, (0, padding), mode='constant', constant_values=0)
            
            # Limit maximum length
            max_samples = int(MAX_DURATION * self.target_sr)
            if len(audio) > max_samples:
                audio = audio[:max_samples]
            
            # Apply gentle high-pass filter to remove low-frequency noise
            audio = librosa.effects.preemphasis(audio, coef=0.97)
            
            return audio, self.target_sr
            
        except Exception as e:
            logger.error(f"Error preprocessing audio: {e}")
            raise
    
    def convert_to_wav(self, input_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert audio file to WAV format.
        
        Args:
            input_path: Input audio file path
            output_path: Output WAV file path (optional)
            
        Returns:
            Path to converted WAV file
        """
        try:
            if output_path is None:
                output_path = tempfile.mktemp(suffix=".wav")
            
            # Load and preprocess audio
            audio, sr = self.load_and_preprocess(input_path)
            
            # Save as WAV
            sf.write(output_path, audio, sr, subtype='PCM_16')
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error converting to WAV: {e}")
            raise
    
    def extract_features(self, audio: np.ndarray, sr: int) -> dict:
        """
        Extract audio features for analysis.
        
        Args:
            audio: Audio array
            sr: Sample rate
            
        Returns:
            Dictionary of audio features
        """
        try:
            features = {}
            
            # Basic properties
            features['duration'] = len(audio) / sr
            features['sample_rate'] = sr
            features['rms_energy'] = np.sqrt(np.mean(audio**2))
            
            # Spectral features
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
            features['spectral_centroid_mean'] = np.mean(spectral_centroids)
            features['spectral_centroid_std'] = np.std(spectral_centroids)
            
            # Zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(audio)[0]
            features['zcr_mean'] = np.mean(zcr)
            
            # MFCC features
            mfccs = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13)
            features['mfcc_mean'] = np.mean(mfccs, axis=1)
            features['mfcc_std'] = np.std(mfccs, axis=1)
            
            # Pitch estimation
            pitches, magnitudes = librosa.piptrack(y=audio, sr=sr)
            pitch_values = []
            for t in range(pitches.shape[1]):
                index = magnitudes[:, t].argmax()
                pitch = pitches[index, t]
                if pitch > 0:
                    pitch_values.append(pitch)
            
            if pitch_values:
                features['pitch_mean'] = np.mean(pitch_values)
                features['pitch_std'] = np.std(pitch_values)
            else:
                features['pitch_mean'] = 0
                features['pitch_std'] = 0
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return {}
    
    def analyze_voice_quality(self, file_path: str) -> dict:
        """
        Analyze voice quality for TTS suitability.
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Dictionary with quality analysis
        """
        try:
            # Load audio
            audio, sr = self.load_and_preprocess(file_path)
            
            # Extract features
            features = self.extract_features(audio, sr)
            
            # Quality assessment
            quality = {
                'overall_score': 0.0,
                'clarity': 0.0,
                'consistency': 0.0,
                'noise_level': 0.0,
                'recommendations': []
            }
            
            # Assess clarity based on spectral centroid
            if features.get('spectral_centroid_mean', 0) > 1000:
                quality['clarity'] = min(1.0, features['spectral_centroid_mean'] / 3000)
            else:
                quality['clarity'] = 0.3
                quality['recommendations'].append("Audio may be muffled or low quality")
            
            # Assess consistency based on RMS energy variation
            if features.get('rms_energy', 0) > 0.01:
                quality['consistency'] = min(1.0, features['rms_energy'] * 10)
            else:
                quality['consistency'] = 0.2
                quality['recommendations'].append("Audio level is very low")
            
            # Assess noise level (inverse of zero crossing rate variation)
            zcr_mean = features.get('zcr_mean', 0)
            if zcr_mean < 0.1:
                quality['noise_level'] = 0.8
            else:
                quality['noise_level'] = max(0.2, 1.0 - zcr_mean)
                if zcr_mean > 0.2:
                    quality['recommendations'].append("Audio may contain background noise")
            
            # Calculate overall score
            quality['overall_score'] = (
                quality['clarity'] * 0.4 + 
                quality['consistency'] * 0.3 + 
                quality['noise_level'] * 0.3
            )
            
            # Add duration-based recommendations
            duration = features.get('duration', 0)
            if duration < 3:
                quality['recommendations'].append("Longer audio samples (3-10s) work better for voice cloning")
            elif duration > 15:
                quality['recommendations'].append("Consider using a shorter clip (3-10s) for better results")
            
            return quality
            
        except Exception as e:
            logger.error(f"Error analyzing voice quality: {e}")
            return {
                'overall_score': 0.0,
                'clarity': 0.0,
                'consistency': 0.0,
                'noise_level': 0.0,
                'recommendations': ['Error analyzing audio quality']
            }

def create_audio_processor() -> AudioProcessor:
    """Create and return an AudioProcessor instance."""
    return AudioProcessor()

def validate_audio_upload(file_path: str) -> Tuple[bool, str, dict]:
    """
    Validate uploaded audio file and return quality analysis.
    
    Args:
        file_path: Path to uploaded audio file
        
    Returns:
        Tuple of (is_valid, message, quality_analysis)
    """
    processor = create_audio_processor()
    
    # Validate file
    is_valid, message = processor.validate_audio_file(file_path)
    
    if not is_valid:
        return False, message, {}
    
    # Analyze quality
    quality = processor.analyze_voice_quality(file_path)
    
    # Generate summary message
    score = quality.get('overall_score', 0)
    if score > 0.7:
        quality_msg = "Excellent audio quality for voice cloning!"
    elif score > 0.5:
        quality_msg = "Good audio quality. Should work well for voice cloning."
    elif score > 0.3:
        quality_msg = "Fair audio quality. May work but results could vary."
    else:
        quality_msg = "Poor audio quality. Consider using a different sample."
    
    return True, quality_msg, quality
