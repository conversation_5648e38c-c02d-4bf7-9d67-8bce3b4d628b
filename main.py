#!/usr/bin/env python3
"""
LLASA 3B TTS Voice Cloning - Main Application
Launch the Gradio web interface for text-to-speech with voice cloning.
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add current directory to Python path
sys.path.append(str(Path(__file__).parent))

from gradio_ui import create_llasa_ui

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        'torch',
        'transformers',
        'gradio',
        'soundfile',
        'torchaudio',
        'numpy',
        'vllm',
        'xcodec2'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error("Missing required packages:")
        for package in missing_packages:
            logger.error(f"  - {package}")
        
        logger.info("\nTo install missing packages, run:")
        if 'xcodec2' in missing_packages:
            logger.info("pip install git+https://github.com/zhenye234/xcodec2.git")
        
        other_packages = [p for p in missing_packages if p != 'xcodec2']
        if other_packages:
            logger.info(f"pip install {' '.join(other_packages)}")
        
        return False
    
    return True

def check_gpu():
    """Check GPU availability and CUDA setup."""
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            
            logger.info(f"GPU detected: {gpu_name}")
            logger.info(f"GPU memory: {gpu_memory:.1f} GB")
            logger.info(f"Number of GPUs: {gpu_count}")
            
            if gpu_memory < 8:
                logger.warning("GPU has less than 8GB memory. Performance may be limited.")
            
            return True
        else:
            logger.warning("No GPU detected. The model will run on CPU (very slow).")
            logger.warning("For best performance, use a GPU with at least 8GB VRAM.")
            return False
            
    except ImportError:
        logger.error("PyTorch not installed. Cannot check GPU status.")
        return False

def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="LLASA 3B TTS Voice Cloning Web Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Launch with default settings
  python main.py --port 8080        # Launch on custom port
  python main.py --share            # Create public shareable link
  python main.py --debug            # Enable debug logging
        """
    )
    
    parser.add_argument(
        '--port', 
        type=int, 
        default=7860,
        help='Port to run the web interface on (default: 7860)'
    )
    
    parser.add_argument(
        '--host', 
        type=str, 
        default='0.0.0.0',
        help='Host to bind the web interface to (default: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--share', 
        action='store_true',
        help='Create a public shareable link via Gradio'
    )
    
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='Enable debug logging'
    )
    
    parser.add_argument(
        '--no-gpu-check', 
        action='store_true',
        help='Skip GPU availability check'
    )
    
    args = parser.parse_args()
    
    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    
    # Print banner
    print("=" * 60)
    print("🎤 LLASA 3B TTS Voice Cloning")
    print("Advanced Text-to-Speech with Zero-Shot Voice Cloning")
    print("=" * 60)
    
    # Check dependencies
    logger.info("Checking dependencies...")
    if not check_dependencies():
        logger.error("Dependency check failed. Please install missing packages.")
        sys.exit(1)
    
    logger.info("✅ All dependencies found!")
    
    # Check GPU (unless skipped)
    if not args.no_gpu_check:
        logger.info("Checking GPU availability...")
        gpu_available = check_gpu()
        
        if not gpu_available:
            response = input("\nNo GPU detected. Continue anyway? (y/N): ")
            if response.lower() not in ['y', 'yes']:
                logger.info("Exiting. Install CUDA and PyTorch with GPU support for best performance.")
                sys.exit(1)
    
    # Create and launch the interface
    try:
        logger.info("Creating Gradio interface...")
        interface = create_llasa_ui()
        
        logger.info(f"Starting web server on {args.host}:{args.port}")
        if args.share:
            logger.info("Creating public shareable link...")
        
        print("\n" + "=" * 60)
        print(f"🚀 Web interface starting at: http://{args.host}:{args.port}")
        if args.share:
            print("📡 Public link will be generated...")
        print("💡 Press Ctrl+C to stop the server")
        print("=" * 60 + "\n")
        
        # Launch the interface
        interface.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            show_error=True,
            quiet=False
        )
        
    except KeyboardInterrupt:
        logger.info("\n🛑 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
