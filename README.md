# LLASA TTS API

A REST API for voice cloning and text-to-speech using the LLASA-3B model from HKUST-Audio.

## Features

- **Text-to-Speech**: Convert text to speech with natural voice synthesis
- **Voice Cloning**: <PERSON>lone voices using audio prompts for zero-shot voice synthesis
- **Multiple Languages**: Support for English and Chinese text
- **REST API**: Easy-to-use HTTP endpoints
- **Audio Processing**: Automatic audio format handling and validation

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd llasa-3b
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install XCodec2 (required for LLASA):
```bash
pip install git+https://github.com/zhenye234/xcodec2.git
```

4. Configure environment:
```bash
cp .env.example .env
# Edit .env with your configuration
```

### Running the API

```bash
python main.py
```

The API will be available at `http://localhost:8000`

## API Endpoints

### Text-to-Speech
- `POST /tts` - Convert text to speech
- `POST /tts/voice-clone` - Convert text to speech with voice cloning

### Health Check
- `GET /health` - Check API status

## Documentation

Visit `http://localhost:8000/docs` for interactive API documentation.

## License

This project uses the LLASA-3B model which is licensed under CC BY-NC 4.0 License.