# 🎤 LLASA 3B TTS Voice Cloning

A user-friendly Gradio web interface for voice cloning and text-to-speech using the LLASA-3B model from HKUST-Audio.

## ✨ Features

- **🎯 Zero-Shot Voice Cloning**: Clone any voice with just a 3-10 second audio sample
- **🗣️ Text-to-Speech**: Convert text to speech with natural voice synthesis
- **🌐 Web Interface**: Easy-to-use Gradio web interface
- **🔊 Audio Processing**: Automatic audio format handling and validation
- **📊 Quality Analysis**: Real-time audio quality assessment for better results
- **🚀 GPU Acceleration**: Optimized for CUDA-enabled GPUs

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- CUDA-capable GPU (recommended, 8GB+ VRAM)
- 16GB+ RAM

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd llasa-3b
```

2. **Install PyTorch with CUDA support:**
```bash
# For CUDA 11.8
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118

# For CUDA 12.1
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121
```

3. **Install other dependencies:**
```bash
pip install -r requirements.txt
```

4. **Install XCodec2 (required for LLASA):**
```bash
pip install git+https://github.com/zhenye234/xcodec2.git
```

5. **Install VLLM:**
```bash
pip install vllm
```

### Running the Application

```bash
python main.py
```

The web interface will be available at `http://localhost:7860`

### Command Line Options

```bash
python main.py --help
```

Options:
- `--port 8080`: Run on custom port
- `--share`: Create public shareable link
- `--debug`: Enable debug logging
- `--no-gpu-check`: Skip GPU availability check

## 🎯 How to Use

1. **Launch the Application**: Run `python main.py` and open the web interface
2. **Load the Model**: Click "Load LLASA Model" and wait for initialization (may take a few minutes)
3. **Upload Voice Sample**: Provide a clear 3-10 second audio sample of the target voice
4. **Enter Text**: Type the text you want to synthesize in the target voice
5. **Generate Speech**: Click "Generate Speech" to create the cloned voice audio

### 💡 Tips for Best Results

- **Voice Sample Quality**: Use clear, noise-free audio samples
- **Duration**: 3-10 seconds works best for voice samples
- **Content**: Natural, conversational speech in the voice sample
- **Format**: WAV, MP3, FLAC, or other common audio formats
- **Single Speaker**: Ensure only one person is speaking in the sample

## 📁 Project Structure

```
llasa-3b/
├── main.py              # Main application entry point
├── gradio_ui.py         # Gradio web interface
├── llasa_tts.py         # LLASA TTS model implementation
├── audio_utils.py       # Audio processing utilities
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## 🔧 Technical Details

### Model Architecture
- **LLASA-3B**: Large Language Audio Speech Assistant with 3B parameters
- **XCodec2**: Neural audio codec for high-quality audio encoding/decoding
- **VLLM**: Optimized inference engine for large language models
- **Whisper**: Automatic speech recognition for audio transcription

### System Requirements
- **GPU**: NVIDIA GPU with 8GB+ VRAM (RTX 3080/4070 or better)
- **RAM**: 16GB+ system memory
- **Storage**: 20GB+ free space for models
- **CUDA**: Version 11.8 or 12.1+

## 🐛 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**:
   - Reduce `gpu_memory_utilization` in `llasa_tts.py`
   - Use shorter audio samples
   - Close other GPU applications

2. **Model Loading Fails**:
   - Check internet connection for model downloads
   - Ensure sufficient disk space
   - Verify CUDA installation

3. **Audio Quality Issues**:
   - Use higher quality voice samples
   - Ensure samples are 3-10 seconds long
   - Remove background noise from samples

4. **Slow Performance**:
   - Ensure GPU is being used (check logs)
   - Update GPU drivers
   - Use CUDA-optimized PyTorch

### Getting Help

If you encounter issues:
1. Check the console logs for error messages
2. Ensure all dependencies are correctly installed
3. Verify your GPU meets the requirements
4. Try with different voice samples

## 📄 License

This project uses the LLASA-3B model which is licensed under CC BY-NC 4.0 License.

## 🙏 Acknowledgments

- HKUST-Audio team for the LLASA-3B model
- XCodec2 developers for the neural audio codec
- Gradio team for the web interface framework