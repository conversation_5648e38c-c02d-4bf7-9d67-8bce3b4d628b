#!/usr/bin/env python3
"""
LLASA 3B TTS API
FastAPI implementation for voice cloning and text-to-speech using LLASA-3B model.
"""

import torch
import numpy as np
import soundfile as sf
import torchaudio
from transformers import pipeline, AutoTokenizer
from xcodec2.modeling_xcodec2 import XCodec2Model
from vllm import LLM, SamplingParams
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from contextlib import asynccontextmanager
import tempfile
import os
import logging
from typing import Optional
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global model instances
llm = None
tokenizer = None
codec_model = None
whisper_pipe = None

def load_models():
    """Load all required models."""
    global llm, tokenizer, codec_model, whisper_pipe
    
    try:
        logger.info("Loading VLLM model...")
        llm = LLM(
            model="srinivasbilla/llasa-3b",
            gpu_memory_utilization=0.5,
            dtype="float32"  # Use float32 for Tesla T4 compatibility
        )
        
        logger.info("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained('srinivasbilla/llasa-3b')
        
        logger.info("Loading XCodec2 model...")
        codec_model = XCodec2Model.from_pretrained("srinivasbilla/xcodec2")
        codec_model.eval().cuda()
        
        logger.info("Loading Whisper model...")
        whisper_pipe = pipeline(
            "automatic-speech-recognition", 
            model="openai/whisper-large-v3-turbo", 
            device='cuda'
        )
        
        logger.info("All models loaded successfully!")
        
    except Exception as e:
        logger.error(f"Error loading models: {e}")
        raise

def ids_to_speech_tokens(speech_ids):
    """Convert speech IDs to speech token strings."""
    speech_tokens_str = []
    for speech_id in speech_ids:
        speech_tokens_str.append(f"<|s_{speech_id}|>")
    return speech_tokens_str

def extract_speech_ids(speech_tokens_str):
    """Extract speech IDs from speech token string."""
    return [int(x.replace('s_', '')) for x in speech_tokens_str[2:-2].split('|><|')]

def preprocess_audio(audio_path: str, max_duration: float = 15.0) -> str:
    """
    Preprocess audio file and trim to max duration.
    
    Args:
        audio_path: Path to the audio file
        max_duration: Maximum duration in seconds (default: 15)
        
    Returns:
        Path to preprocessed audio file (16kHz WAV)
    """
    try:
        # Load audio
        waveform, sample_rate = torchaudio.load(audio_path)

        # Check if the audio is stereo (convert to mono if needed)
        if waveform.size(0) > 1:
            waveform_mono = torch.mean(waveform, dim=0, keepdim=True)
        else:
            waveform_mono = waveform

        # Resample to 16kHz (required by LLASA)
        waveform_16k = torchaudio.transforms.Resample(
            orig_freq=sample_rate, 
            new_freq=16000
        )(waveform_mono)
        
        # Trim to max duration
        max_samples = int(max_duration * 16000)
        if waveform_16k.size(1) > max_samples:
            waveform_16k = waveform_16k[:, :max_samples]
        
        # Save preprocessed audio
        output_path = tempfile.mktemp(suffix=".wav")
        torchaudio.save(output_path, waveform_16k, 16000)
        
        return output_path
        
    except Exception as e:
        logger.error(f"Error preprocessing audio: {e}")
        raise

def text_to_speech(sample_audio_path: str, target_text: str, prompt_text: Optional[str] = None) -> np.ndarray:
    """
    Generate speech from text with voice cloning.
    
    Args:
        sample_audio_path: Path to audio file for voice cloning
        target_text: Text to synthesize
        prompt_text: Optional prompt text (if None, will be transcribed from audio)
        
    Returns:
        Generated audio as numpy array
    """
    try:
        # Preprocess audio to 16kHz and trim to 15 seconds
        processed_audio_path = preprocess_audio(sample_audio_path, max_duration=15.0)
        
        # Load the processed audio
        prompt_wav, _ = sf.read(processed_audio_path)
        prompt_wav = torch.from_numpy(prompt_wav).float().unsqueeze(0)

        # Transcribe audio if prompt_text not provided
        if prompt_text is None:
            prompt_text = whisper_pipe(processed_audio_path)['text'].strip()
            logger.info(f"Transcribed prompt: {prompt_text}")

        input_text = prompt_text + ' ' + target_text

        # Calculate dynamic max_tokens based on text length
        # Rough estimate: 1 token per character + buffer for speech tokens
        estimated_tokens = len(input_text) * 2 + 1000
        max_tokens = min(max(estimated_tokens, 1024), 4096)  # Between 1024 and 4096

        # Create sampling params with dynamic max_tokens and beam search disabled
        sampling_params = SamplingParams(
            temperature=0.8, 
            top_p=1, 
            max_tokens=max_tokens,
            use_beam_search=False,  # Disable beam search
            stop=['<|SPEECH_GENERATION_END|>'], 
            stop_token_ids=[128261]
        )

        # TTS generation
        with torch.no_grad():
            # Encode the prompt wav using XCodec2
            vq_code_prompt = codec_model.encode_code(input_waveform=prompt_wav)
            vq_code_prompt = vq_code_prompt[0, 0, :]
            
            # Convert int codes to speech tokens
            speech_ids_prefix = ids_to_speech_tokens(vq_code_prompt)

            formatted_text = f"<|TEXT_UNDERSTANDING_START|>{input_text}<|TEXT_UNDERSTANDING_END|>"

            # Prepare chat format
            chat = [
                {"role": "user", "content": "Convert the text to speech:" + formatted_text},
                {"role": "assistant", "content": "<|SPEECH_GENERATION_START|>" + ''.join(speech_ids_prefix)}
            ]

            # Apply chat template
            input_ids = tokenizer.apply_chat_template(
                chat, 
                tokenize=False, 
                continue_final_message=True
            )

            # Generate with VLLM
            outputs = llm.generate([input_ids], sampling_params)
            generated_text = outputs[0].outputs[0].text

            # Extract speech tokens and decode
            speech_tokens = extract_speech_ids(generated_text)
            speech_tokens = torch.tensor(speech_tokens).cuda().unsqueeze(0).unsqueeze(0)
            gen_wav = codec_model.decode_code(speech_tokens)
        
        # Clean up temporary file
        if os.path.exists(processed_audio_path):
            os.remove(processed_audio_path)
        
        return gen_wav[0, 0, :].cpu().numpy()
        
    except Exception as e:
        logger.error(f"Error generating speech: {e}")
        raise

@asynccontextmanager
async def lifespan(_: FastAPI):
    """Lifespan context manager for model loading."""
    logger.info("Starting LLASA 3B TTS API...")
    load_models()
    logger.info("API ready!")
    yield
    logger.info("Shutting down API...")

app = FastAPI(
    title="LLASA 3B TTS API",
    description="Voice cloning and text-to-speech API using LLASA-3B model",
    version="1.0.0",
    lifespan=lifespan
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "LLASA 3B TTS API", "status": "ready"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "models_loaded": all([llm, tokenizer, codec_model, whisper_pipe])
    }

@app.post("/tts")
async def generate_tts(
    reference_audio: UploadFile = File(..., description="Reference audio file for voice cloning"),
    text: str = Form(..., description="Text to synthesize"),
    prompt_text: Optional[str] = Form(None, description="Optional prompt text (auto-transcribed if not provided)")
):
    """
    Generate text-to-speech with voice cloning.
    
    Args:
        reference_audio: Audio file for voice cloning (will be trimmed to 15 seconds)
        text: Text to synthesize
        prompt_text: Optional prompt text (if not provided, will be auto-transcribed)
        
    Returns:
        Generated audio file
    """
    try:
        # Validate inputs
        if not text or not text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")
        
        # Check if models are loaded
        if not all([llm, tokenizer, codec_model, whisper_pipe]):
            raise HTTPException(status_code=503, detail="Models not loaded")
        
        # Save uploaded audio to temporary file
        temp_audio_path = tempfile.mktemp(suffix=".wav")
        with open(temp_audio_path, "wb") as buffer:
            content = await reference_audio.read()
            buffer.write(content)
        
        # Generate speech
        logger.info(f"Generating TTS for text: '{text[:50]}...'")
        generated_audio = text_to_speech(
            sample_audio_path=temp_audio_path,
            target_text=text.strip(),
            prompt_text=prompt_text.strip() if prompt_text else None
        )
        
        # Save generated audio
        output_path = tempfile.mktemp(suffix=".wav")
        sf.write(output_path, generated_audio, 16000)
        
        # Clean up input file
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        
        logger.info("TTS generation completed successfully!")
        
        # Return the generated audio file
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename="generated_speech.wav",
            background=lambda: os.remove(output_path) if os.path.exists(output_path) else None
        )
        
    except Exception as e:
        # Clean up files on error
        if 'temp_audio_path' in locals() and os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        if 'output_path' in locals() and os.path.exists(output_path):
            os.remove(output_path)
        
        logger.error(f"Error in TTS generation: {e}")
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
