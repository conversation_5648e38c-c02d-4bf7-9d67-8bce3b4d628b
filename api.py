#!/usr/bin/env python3
"""
LLASA 3B TTS API
FastAPI implementation for voice cloning and text-to-speech using LLASA-3B model.
"""

import torch
import numpy as np
import soundfile as sf
import torchaudio
from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
from xcodec2.modeling_xcodec2 import XCodec2Model
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from contextlib import asynccontextmanager
import tempfile
import os
import logging
from typing import Optional
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global model instances
llm = None
tokenizer = None
codec_model = None
whisper_pipe = None

def load_models():
    """Load all required models."""
    global llm, tokenizer, codec_model, whisper_pipe
    
    try:
        logger.info("Loading VLLM model...")
        try:
            # Try with minimal memory first
            llm = LLM(
                model="srinivasbilla/llasa-3b",
                gpu_memory_utilization=0.4,  # Very conservative for Tesla T4
                dtype="float16",
                enforce_eager=True,  # Disable CUDA graphs to save memory
                disable_log_stats=True  # Reduce memory overhead
            )
        except Exception as e:
            logger.warning(f"Failed to load with 0.4 utilization: {e}")
            # Try even more conservative settings
            llm = LLM(
                model="srinivasbilla/llasa-3b",
                gpu_memory_utilization=0.3,  # Even more conservative
                dtype="float16",
                enforce_eager=True,
                disable_log_stats=True
            )
        
        logger.info("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained('srinivasbilla/llasa-3b')
        
        logger.info("Loading XCodec2 model...")
        codec_model = XCodec2Model.from_pretrained("srinivasbilla/xcodec2")
        codec_model.eval().cuda().half()  # Use float16 for memory efficiency
        
        logger.info("Loading Whisper model...")
        whisper_pipe = pipeline(
            "automatic-speech-recognition", 
            model="openai/whisper-large-v3-turbo", 
            device='cuda'
        )
        
        logger.info("All models loaded successfully!")
        
    except Exception as e:
        logger.error(f"Error loading models: {e}")
        raise

def ids_to_speech_tokens(speech_ids):
    """Convert speech IDs to speech token strings."""
    speech_tokens_str = []
    for speech_id in speech_ids:
        speech_tokens_str.append(f"<|s_{speech_id}|>")
    return speech_tokens_str

def extract_speech_ids(speech_tokens_str):
    """Extract speech IDs from speech token string."""
    return [int(x.replace('s_', '')) for x in speech_tokens_str[2:-2].split('|><|')]

def cleanup_gpu_memory():
    """Clean up GPU memory and cache."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

def preprocess_audio(audio_path: str, max_duration: float = 15.0) -> str:
    """
    Preprocess audio file and trim to max duration.

    Args:
        audio_path: Path to the audio file
        max_duration: Maximum duration in seconds (default: 15)

    Returns:
        Path to preprocessed audio file (16kHz WAV)
    """
    try:
        # First try with torchaudio
        try:
            waveform, sample_rate = torchaudio.load(audio_path)
        except Exception as e:
            logger.warning(f"torchaudio failed to load {audio_path}: {e}")
            # Fallback to soundfile for MP3 and other formats
            import soundfile as sf
            audio_data, sample_rate = sf.read(audio_path)
            # Convert to torch tensor
            if len(audio_data.shape) == 1:
                waveform = torch.from_numpy(audio_data).float().unsqueeze(0)
            else:
                waveform = torch.from_numpy(audio_data.T).float()

        # Check if the audio is stereo (convert to mono if needed)
        if waveform.size(0) > 1:
            waveform_mono = torch.mean(waveform, dim=0, keepdim=True)
        else:
            waveform_mono = waveform

        # Resample to 16kHz (required by LLASA)
        if sample_rate != 16000:
            waveform_16k = torchaudio.transforms.Resample(
                orig_freq=sample_rate,
                new_freq=16000
            )(waveform_mono)
        else:
            waveform_16k = waveform_mono

        # Trim to max duration
        max_samples = int(max_duration * 16000)
        if waveform_16k.size(1) > max_samples:
            waveform_16k = waveform_16k[:, :max_samples]

        # Ensure minimum duration (at least 0.5 seconds)
        min_samples = int(0.5 * 16000)
        if waveform_16k.size(1) < min_samples:
            logger.warning(f"Audio too short ({waveform_16k.size(1)/16000:.2f}s), padding to 0.5s")
            padding = min_samples - waveform_16k.size(1)
            waveform_16k = torch.nn.functional.pad(waveform_16k, (0, padding))

        # Save preprocessed audio
        output_path = tempfile.mktemp(suffix=".wav")
        torchaudio.save(output_path, waveform_16k, 16000)

        logger.info(f"Preprocessed audio: {waveform_16k.size(1)/16000:.2f}s at 16kHz")
        return output_path

    except Exception as e:
        logger.error(f"Error preprocessing audio: {e}")
        raise

def text_to_speech(sample_audio_path: str, target_text: str, prompt_text: Optional[str] = None) -> np.ndarray:
    """
    Generate speech from text with voice cloning.
    
    Args:
        sample_audio_path: Path to audio file for voice cloning
        target_text: Text to synthesize
        prompt_text: Optional prompt text (if None, will be transcribed from audio)
        
    Returns:
        Generated audio as numpy array
    """
    try:
        # Preprocess audio to 16kHz and trim to 15 seconds
        processed_audio_path = preprocess_audio(sample_audio_path, max_duration=15.0)
        
        # Load the processed audio (already 16kHz WAV from preprocessing)
        try:
            prompt_wav, _ = sf.read(processed_audio_path)
            prompt_wav = torch.from_numpy(prompt_wav).float().unsqueeze(0)
        except Exception as e:
            logger.error(f"Error loading processed audio: {e}")
            # Fallback to torchaudio
            prompt_wav, _ = torchaudio.load(processed_audio_path)
            if prompt_wav.size(0) > 1:
                prompt_wav = torch.mean(prompt_wav, dim=0, keepdim=True)

        # Transcribe audio if prompt_text not provided
        if prompt_text is None:
            prompt_text = whisper_pipe(processed_audio_path)['text'].strip()
            logger.info(f"Transcribed prompt: {prompt_text}")

        input_text = prompt_text + ' ' + target_text

        # Calculate dynamic max_tokens based on text length (reduced for memory)
        # Rough estimate: 1 token per character + buffer for speech tokens
        estimated_tokens = len(input_text) * 1.5 + 500  # Reduced multiplier and buffer
        max_tokens = min(max(estimated_tokens, 512), 2048)  # Reduced range: 512-2048

        # Create sampling params with dynamic max_tokens and beam search disabled
        sampling_params = SamplingParams(
            temperature=0.8,
            top_p=0.9,  # Reduced from 1.0 for better memory usage
            max_tokens=max_tokens,
            use_beam_search=False,  # Disable beam search
            stop=['<|SPEECH_GENERATION_END|>'],
            stop_token_ids=[128261]
        )

        # TTS generation with memory optimization
        with torch.no_grad():
            # Clear GPU cache before processing
            torch.cuda.empty_cache()

            # Encode the prompt wav using XCodec2
            prompt_wav = prompt_wav.half().cuda()  # Ensure float16 and on GPU
            vq_code_prompt = codec_model.encode_code(input_waveform=prompt_wav)
            vq_code_prompt = vq_code_prompt[0, 0, :]

            # Clear prompt wav from GPU memory
            del prompt_wav
            torch.cuda.empty_cache()

            # Convert int codes to speech tokens
            speech_ids_prefix = ids_to_speech_tokens(vq_code_prompt)

            formatted_text = f"<|TEXT_UNDERSTANDING_START|>{input_text}<|TEXT_UNDERSTANDING_END|>"

            # Prepare chat format
            chat = [
                {"role": "user", "content": "Convert the text to speech:" + formatted_text},
                {"role": "assistant", "content": "<|SPEECH_GENERATION_START|>" + ''.join(speech_ids_prefix)}
            ]

            # Apply chat template
            input_ids = tokenizer.apply_chat_template(
                chat,
                tokenize=False,
                continue_final_message=True
            )

            # Clear cache before generation
            torch.cuda.empty_cache()

            # Generate with VLLM
            outputs = llm.generate([input_ids], sampling_params)
            generated_text = outputs[0].outputs[0].text

            # Clear generation cache
            torch.cuda.empty_cache()

            # Extract speech tokens and decode
            speech_tokens = extract_speech_ids(generated_text)
            speech_tokens = torch.tensor(speech_tokens, dtype=torch.long).cuda().unsqueeze(0).unsqueeze(0)
            gen_wav = codec_model.decode_code(speech_tokens)

            # Clear speech tokens from GPU
            del speech_tokens
            torch.cuda.empty_cache()
        
        # Clean up temporary file
        if os.path.exists(processed_audio_path):
            os.remove(processed_audio_path)
        
        return gen_wav[0, 0, :].cpu().numpy()
        
    except Exception as e:
        logger.error(f"Error generating speech: {e}")
        raise

@asynccontextmanager
async def lifespan(_: FastAPI):
    """Lifespan context manager for model loading."""
    logger.info("Starting LLASA 3B TTS API...")
    load_models()
    logger.info("API ready!")
    yield
    logger.info("Shutting down API...")

app = FastAPI(
    title="LLASA 3B TTS API",
    description="Voice cloning and text-to-speech API using LLASA-3B model",
    version="1.0.0",
    lifespan=lifespan
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "LLASA 3B TTS API", "status": "ready"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "models_loaded": all([llm, tokenizer, codec_model, whisper_pipe])
    }

@app.post("/tts")
async def generate_tts(
    reference_audio: UploadFile = File(..., description="Reference audio file for voice cloning"),
    text: str = Form(..., description="Text to synthesize"),
    prompt_text: Optional[str] = Form(None, description="Optional prompt text (auto-transcribed if not provided)")
):
    """
    Generate text-to-speech with voice cloning.
    
    Args:
        reference_audio: Audio file for voice cloning (will be trimmed to 15 seconds)
        text: Text to synthesize
        prompt_text: Optional prompt text (if not provided, will be auto-transcribed)
        
    Returns:
        Generated audio file
    """
    try:
        # Validate inputs
        if not text or not text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")
        
        # Check if models are loaded
        if not all([llm, tokenizer, codec_model, whisper_pipe]):
            raise HTTPException(status_code=503, detail="Models not loaded")
        
        # Save uploaded audio to temporary file
        temp_audio_path = tempfile.mktemp(suffix=".wav")
        with open(temp_audio_path, "wb") as buffer:
            content = await reference_audio.read()
            buffer.write(content)
        
        # Generate speech
        logger.info(f"Generating TTS for text: '{text[:50]}...'")
        generated_audio = text_to_speech(
            sample_audio_path=temp_audio_path,
            target_text=text.strip(),
            prompt_text=prompt_text.strip() if prompt_text else None
        )
        
        # Save generated audio
        output_path = tempfile.mktemp(suffix=".wav")
        sf.write(output_path, generated_audio, 16000)
        
        # Clean up input file
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)

        # Final GPU memory cleanup
        cleanup_gpu_memory()

        logger.info("TTS generation completed successfully!")

        # Return the generated audio file
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename="generated_speech.wav",
            background=lambda: os.remove(output_path) if os.path.exists(output_path) else None
        )
        
    except Exception as e:
        # Clean up files on error
        if 'temp_audio_path' in locals() and os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        if 'output_path' in locals() and os.path.exists(output_path):
            os.remove(output_path)
        
        logger.error(f"Error in TTS generation: {e}")
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
