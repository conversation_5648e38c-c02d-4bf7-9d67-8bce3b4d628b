#!/usr/bin/env python3
"""
LLASA 3B TTS API
FastAPI implementation using the exact same approach as the Gradio UI.
"""

from llasa_tts import LLASATTSModel
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileR<PERSON>ponse
from contextlib import asynccontextmanager
import tempfile
import os
import logging
from typing import Optional
import uvicorn
import torch
import torchaudio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global model instance
tts_model = None

def load_models():
    """Load the LLASA TTS model using the existing implementation."""
    global tts_model

    try:
        logger.info("Loading LLASA TTS model...")
        tts_model = LLASATTSModel()
        logger.info("Model loaded successfully!")

    except Exception as e:
        logger.error(f"Error loading models: {e}")
        raise

def preprocess_audio_with_trimming(audio_path: str, max_duration: float = 15.0) -> str:
    """
    Preprocess audio file and trim to max duration.

    Args:
        audio_path: Path to the audio file
        max_duration: Maximum duration in seconds (default: 15)

    Returns:
        Path to preprocessed audio file (16kHz WAV)
    """
    try:
        # First try with torchaudio
        try:
            waveform, sample_rate = torchaudio.load(audio_path)
        except Exception as e:
            logger.warning(f"torchaudio failed to load {audio_path}: {e}")
            # Fallback to soundfile for MP3 and other formats
            import soundfile as sf
            audio_data, sample_rate = sf.read(audio_path)
            # Convert to torch tensor
            if len(audio_data.shape) == 1:
                waveform = torch.from_numpy(audio_data).float().unsqueeze(0)
            else:
                waveform = torch.from_numpy(audio_data.T).float()

        # Check if the audio is stereo (convert to mono if needed)
        if waveform.size(0) > 1:
            waveform_mono = torch.mean(waveform, dim=0, keepdim=True)
        else:
            waveform_mono = waveform

        # Resample to 16kHz (required by LLASA)
        if sample_rate != 16000:
            waveform_16k = torchaudio.transforms.Resample(
                orig_freq=sample_rate,
                new_freq=16000
            )(waveform_mono)
        else:
            waveform_16k = waveform_mono

        # Trim to max duration
        max_samples = int(max_duration * 16000)
        if waveform_16k.size(1) > max_samples:
            waveform_16k = waveform_16k[:, :max_samples]

        # Ensure minimum duration (at least 0.5 seconds)
        min_samples = int(0.5 * 16000)
        if waveform_16k.size(1) < min_samples:
            logger.warning(f"Audio too short ({waveform_16k.size(1)/16000:.2f}s), padding to 0.5s")
            padding = min_samples - waveform_16k.size(1)
            waveform_16k = torch.nn.functional.pad(waveform_16k, (0, padding))

        # Save preprocessed audio
        output_path = tempfile.mktemp(suffix=".wav")
        torchaudio.save(output_path, waveform_16k, 16000)

        logger.info(f"Preprocessed audio: {waveform_16k.size(1)/16000:.2f}s at 16kHz")
        return output_path

    except Exception as e:
        logger.error(f"Error preprocessing audio: {e}")
        raise

# API endpoints

@asynccontextmanager
async def lifespan(_: FastAPI):
    """Lifespan context manager for model loading."""
    logger.info("Starting LLASA 3B TTS API...")
    load_models()
    logger.info("API ready!")
    yield
    logger.info("Shutting down API...")

app = FastAPI(
    title="LLASA 3B TTS API",
    description="Voice cloning and text-to-speech API using LLASA-3B model",
    version="1.0.0",
    lifespan=lifespan
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "LLASA 3B TTS API", "status": "ready"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "models_loaded": tts_model is not None
    }

@app.post("/tts")
async def generate_tts(
    reference_audio: UploadFile = File(..., description="Reference audio file for voice cloning"),
    text: str = Form(..., description="Text to synthesize"),
    prompt_text: Optional[str] = Form(None, description="Optional prompt text (auto-transcribed if not provided)")
):
    """
    Generate text-to-speech with voice cloning.
    
    Args:
        reference_audio: Audio file for voice cloning (will be trimmed to 15 seconds)
        text: Text to synthesize
        prompt_text: Optional prompt text (if not provided, will be auto-transcribed)
        
    Returns:
        Generated audio file
    """
    try:
        # Validate inputs
        if not text or not text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")
        
        # Check if models are loaded
        if not all([llm, tokenizer, codec_model, whisper_pipe]):
            raise HTTPException(status_code=503, detail="Models not loaded")
        
        # Save uploaded audio to temporary file
        temp_audio_path = tempfile.mktemp(suffix=".wav")
        with open(temp_audio_path, "wb") as buffer:
            content = await reference_audio.read()
            buffer.write(content)
        
        # Preprocess audio with 15-second trimming
        processed_audio_path = preprocess_audio_with_trimming(temp_audio_path, max_duration=15.0)

        # Generate speech using the LLASA model
        logger.info(f"Generating TTS for text: '{text[:50]}...'")
        generated_audio = tts_model.text_to_speech(
            sample_audio_path=processed_audio_path,
            target_text=text.strip(),
            prompt_text=prompt_text.strip() if prompt_text else None
        )

        # Save generated audio
        output_path = tempfile.mktemp(suffix=".wav")
        import soundfile as sf
        sf.write(output_path, generated_audio, 16000)

        # Clean up processed audio
        if os.path.exists(processed_audio_path):
            os.remove(processed_audio_path)
        
        # Clean up input file
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)

        # Final GPU memory cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        logger.info("TTS generation completed successfully!")

        # Return the generated audio file
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename="generated_speech.wav",
            background=lambda: os.remove(output_path) if os.path.exists(output_path) else None
        )
        
    except Exception as e:
        # Clean up files on error
        if 'temp_audio_path' in locals() and os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        if 'output_path' in locals() and os.path.exists(output_path):
            os.remove(output_path)
        
        logger.error(f"Error in TTS generation: {e}")
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
