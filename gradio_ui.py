"""
Gradio UI for LLASA 3B TTS Voice Cloning
A user-friendly web interface for text-to-speech with voice cloning capabilities.
"""

import gradio as gr
import tempfile
import os
import logging
from typing import Optional, Tuple
import traceback

from llasa_tts import LLASATTSModel
from audio_utils import validate_audio_upload

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLASAGradioUI:
    """Gradio UI wrapper for LLASA TTS model."""
    
    def __init__(self):
        """Initialize the UI with model loading."""
        self.model = None
        self.model_loaded = False
        
    def load_model(self):
        """Load the LLASA TTS model."""
        try:
            if not self.model_loaded:
                logger.info("Loading LLASA TTS model...")
                self.model = LLASATTSModel()
                self.model_loaded = True
                logger.info("Model loaded successfully!")
                return "✅ Model loaded successfully!"
            else:
                return "✅ Model already loaded!"
        except Exception as e:
            error_msg = f"❌ Error loading model: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return error_msg
    
    def generate_speech(
        self, 
        voice_sample, 
        target_text: str, 
        prompt_text: Optional[str] = None
    ) -> Tuple[Optional[str], str]:
        """
        Generate speech with voice cloning.
        
        Args:
            voice_sample: Uploaded audio file for voice cloning
            target_text: Text to synthesize
            prompt_text: Optional prompt text (if empty, will be auto-transcribed)
            
        Returns:
            Tuple of (audio_file_path, status_message)
        """
        try:
            # Check if model is loaded
            if not self.model_loaded or self.model is None:
                return None, "❌ Please load the model first!"
            
            # Validate inputs
            if not target_text or not target_text.strip():
                return None, "❌ Please enter text to synthesize!"
            
            if voice_sample is None:
                return None, "❌ Please upload a voice sample!"
            
            # Validate audio file
            is_valid, message, quality = validate_audio_upload(voice_sample)
            if not is_valid:
                return None, f"❌ Audio validation failed: {message}"
            
            # Clean prompt text
            if prompt_text and prompt_text.strip():
                prompt_text = prompt_text.strip()
            else:
                prompt_text = None
            
            # Generate speech
            logger.info(f"Generating speech for text: '{target_text[:50]}...'")
            
            output_path = tempfile.mktemp(suffix=".wav")
            generated_audio = self.model.text_to_speech(
                sample_audio_path=voice_sample,
                target_text=target_text.strip(),
                prompt_text=prompt_text
            )
            
            # Save the generated audio
            final_path = self.model.save_audio(generated_audio, output_path)
            
            # Create status message with quality info
            quality_score = quality.get('overall_score', 0)
            status_msg = f"✅ Speech generated successfully!\n"
            status_msg += f"Voice sample quality: {quality_score:.1%}\n"
            
            if quality.get('recommendations'):
                status_msg += "💡 Tips for better results:\n"
                for rec in quality['recommendations'][:2]:  # Show max 2 recommendations
                    status_msg += f"• {rec}\n"
            
            logger.info("Speech generation completed successfully!")
            return final_path, status_msg
            
        except Exception as e:
            error_msg = f"❌ Error generating speech: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return None, error_msg
    
    def create_interface(self):
        """Create and return the Gradio interface."""
        
        # Custom CSS for better styling
        css = """
        .gradio-container {
            max-width: 1200px !important;
        }
        .main-header {
            text-align: center;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .section-header {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 10px 0;
        }
        """
        
        with gr.Blocks(css=css, title="LLASA 3B TTS Voice Cloning") as interface:
            
            # Header
            gr.HTML("""
                <div class="main-header">
                    <h1>🎤 LLASA 3B TTS Voice Cloning</h1>
                    <p>Advanced text-to-speech with zero-shot voice cloning capabilities</p>
                </div>
            """)
            
            # Model loading section
            with gr.Row():
                with gr.Column():
                    gr.HTML('<div class="section-header"><h3>🚀 Model Setup</h3></div>')
                    load_btn = gr.Button("Load LLASA Model", variant="primary", size="lg")
                    model_status = gr.Textbox(
                        label="Model Status",
                        value="Click 'Load LLASA Model' to initialize the model",
                        interactive=False
                    )
            
            # Main TTS interface
            gr.HTML('<div class="section-header"><h3>🎯 Voice Cloning & Text-to-Speech</h3></div>')
            
            with gr.Row():
                # Input column
                with gr.Column(scale=1):
                    gr.Markdown("### 📤 Input")
                    
                    voice_sample = gr.Audio(
                        label="Voice Sample for Cloning",
                        type="filepath",
                        sources=["upload", "microphone"]
                    )
                    
                    gr.Markdown("""
                    **Voice Sample Tips:**
                    - Use clear, high-quality audio (3-10 seconds)
                    - Single speaker, minimal background noise
                    - Natural speech pace and tone
                    """)
                    
                    target_text = gr.Textbox(
                        label="Text to Synthesize",
                        placeholder="Enter the text you want to convert to speech...",
                        lines=4,
                        max_lines=8
                    )
                    
                    prompt_text = gr.Textbox(
                        label="Prompt Text (Optional)",
                        placeholder="Leave empty for auto-transcription, or enter the text spoken in the voice sample",
                        lines=2
                    )
                    
                    generate_btn = gr.Button(
                        "🎵 Generate Speech", 
                        variant="primary", 
                        size="lg"
                    )
                
                # Output column
                with gr.Column(scale=1):
                    gr.Markdown("### 📥 Output")
                    
                    output_audio = gr.Audio(
                        label="Generated Speech",
                        type="filepath"
                    )
                    
                    status_output = gr.Textbox(
                        label="Status",
                        lines=6,
                        interactive=False
                    )
            
            # Examples section
            gr.HTML('<div class="section-header"><h3>💡 Example Usage</h3></div>')
            
            with gr.Row():
                gr.Markdown("""
                ### How to use:
                1. **Load Model**: Click "Load LLASA Model" and wait for initialization
                2. **Upload Voice Sample**: Provide a clear audio sample (3-10 seconds) of the target voice
                3. **Enter Text**: Type the text you want to synthesize in the target voice
                4. **Optional Prompt**: Leave empty for auto-transcription, or manually enter what was said in the voice sample
                5. **Generate**: Click "Generate Speech" to create the cloned voice audio
                
                ### Best Results:
                - Use high-quality, noise-free voice samples
                - Keep voice samples between 3-10 seconds
                - Ensure clear pronunciation in the voice sample
                - Use natural, conversational text for synthesis
                """)
            
            # Event handlers
            load_btn.click(
                fn=self.load_model,
                outputs=[model_status]
            )
            
            generate_btn.click(
                fn=self.generate_speech,
                inputs=[voice_sample, target_text, prompt_text],
                outputs=[output_audio, status_output]
            )
            
            # Auto-clear status when new inputs are provided
            target_text.change(
                fn=lambda: "Ready to generate speech...",
                outputs=[status_output]
            )
            
            voice_sample.change(
                fn=lambda: "Ready to generate speech...",
                outputs=[status_output]
            )
        
        return interface

def create_llasa_ui():
    """Create and return the LLASA Gradio UI."""
    ui = LLASAGradioUI()
    return ui.create_interface()

if __name__ == "__main__":
    # Create and launch the interface
    interface = create_llasa_ui()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
