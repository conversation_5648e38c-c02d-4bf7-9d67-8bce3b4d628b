"""
LLASA 3B TTS Model Implementation
A text-to-speech and voice cloning system using the LLASA-3B model with VLLM and XCodec2.
"""

import torch
import numpy as np
import soundfile as sf
import torchaudio
from transformers import pipeline, AutoTokenizer
from xcodec2.modeling_xcodec2 import XCodec2Model
from vllm import LLM, SamplingParams
from typing import Optional, Union, Tuple
import tempfile
import os
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLASATTSModel:
    """LLASA 3B Text-to-Speech Model with Voice Cloning capabilities using VLLM and XCodec2."""

    def __init__(self, model_name: str = "srinivasbilla/llasa-3b", codec_model_name: str = "srinivasbilla/xcodec2"):
        """
        Initialize the LLASA TTS model.

        Args:
            model_name: HuggingFace model name for LLASA
            codec_model_name: HuggingFace model name for XCodec2
        """
        self.model_name = model_name
        self.codec_model_name = codec_model_name
        self.sample_rate = 16000  # LLASA uses 16kHz

        logger.info("Initializing LLASA TTS with VLLM and XCodec2...")
        self._load_models()

    def _load_models(self):
        """Load the LLASA model, tokenizer, codec, and whisper."""
        try:
            logger.info("Loading VLLM model...")
            self.llm = LLM(
                model=self.model_name,
                gpu_memory_utilization=0.5,
                max_model_len=4096
            )

            logger.info("Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)

            logger.info("Loading XCodec2 model...")
            self.codec_model = XCodec2Model.from_pretrained(self.codec_model_name)
            self.codec_model.eval().cuda()

            logger.info("Loading Whisper model...")
            self.whisper_pipe = pipeline(
                "automatic-speech-recognition",
                model="openai/whisper-large-v3-turbo",
                device='cuda'
            )

            # Set up sampling parameters
            self.sampling_params = SamplingParams(
                temperature=0.8,
                top_p=1,
                max_tokens=2048,
                stop=['<|SPEECH_GENERATION_END|>'],
                stop_token_ids=[128261]
            )

            logger.info("All models loaded successfully!")

        except Exception as e:
            logger.error(f"Error loading models: {e}")
            raise
    
    def ids_to_speech_tokens(self, speech_ids):
        """Convert speech IDs to speech token strings."""
        speech_tokens_str = []
        for speech_id in speech_ids:
            speech_tokens_str.append(f"<|s_{speech_id}|>")
        return speech_tokens_str

    def extract_speech_ids(self, speech_tokens_str):
        """Extract speech IDs from speech token string."""
        return [int(x.replace('s_', '')) for x in speech_tokens_str[2:-2].split('|><|')]

    def preprocess_audio(self, audio_path: str) -> str:
        """
        Preprocess audio file for voice cloning.

        Args:
            audio_path: Path to the audio file

        Returns:
            Path to preprocessed audio file (16kHz WAV)
        """
        try:
            # Load audio
            waveform, sample_rate = torchaudio.load(audio_path)

            # Check if the audio is stereo (convert to mono if needed)
            if waveform.size(0) > 1:
                # Convert stereo to mono by averaging the channels
                waveform_mono = torch.mean(waveform, dim=0, keepdim=True)
            else:
                # If already mono, use the original waveform
                waveform_mono = waveform

            # Resample to 16kHz (required by LLASA)
            waveform_16k = torchaudio.transforms.Resample(
                orig_freq=sample_rate,
                new_freq=16000
            )(waveform_mono)

            # Save preprocessed audio
            output_path = tempfile.mktemp(suffix=".wav")
            torchaudio.save(output_path, waveform_16k, 16000)

            return output_path

        except Exception as e:
            logger.error(f"Error preprocessing audio: {e}")
            raise
    
    def text_to_speech(
        self,
        sample_audio_path: str,
        target_text: str,
        prompt_text: Optional[str] = None
    ) -> np.ndarray:
        """
        Generate speech from text with voice cloning using the reference implementation.

        Args:
            sample_audio_path: Path to audio file for voice cloning
            target_text: Text to synthesize
            prompt_text: Optional prompt text (if None, will be transcribed from audio)

        Returns:
            Generated audio as numpy array
        """
        try:
            # Preprocess audio to 16kHz
            processed_audio_path = self.preprocess_audio(sample_audio_path)

            # Load the processed audio
            prompt_wav, sr = sf.read(processed_audio_path)
            prompt_wav = torch.from_numpy(prompt_wav).float().unsqueeze(0)

            # Transcribe audio if prompt_text not provided
            if prompt_text is None:
                prompt_text = self.whisper_pipe(processed_audio_path)['text'].strip()
                logger.info(f"Transcribed prompt: {prompt_text}")

            input_text = prompt_text + ' ' + target_text

            # TTS generation
            with torch.no_grad():
                # Encode the prompt wav using XCodec2
                vq_code_prompt = self.codec_model.encode_code(input_waveform=prompt_wav)
                vq_code_prompt = vq_code_prompt[0, 0, :]

                # Convert int codes to speech tokens
                speech_ids_prefix = self.ids_to_speech_tokens(vq_code_prompt)

                formatted_text = f"<|TEXT_UNDERSTANDING_START|>{input_text}<|TEXT_UNDERSTANDING_END|>"

                # Prepare chat format
                chat = [
                    {"role": "user", "content": "Convert the text to speech:" + formatted_text},
                    {"role": "assistant", "content": "<|SPEECH_GENERATION_START|>" + ''.join(speech_ids_prefix)}
                ]

                # Apply chat template
                input_ids = self.tokenizer.apply_chat_template(
                    chat,
                    tokenize=False,
                    continue_final_message=True
                )

                # Generate with VLLM
                outputs = self.llm.generate([input_ids], self.sampling_params)
                generated_text = outputs[0].outputs[0].text

                # Extract speech tokens and decode
                speech_tokens = self.extract_speech_ids(generated_text)
                speech_tokens = torch.tensor(speech_tokens).cuda().unsqueeze(0).unsqueeze(0)
                gen_wav = self.codec_model.decode_code(speech_tokens)

            # Clean up temporary file
            if os.path.exists(processed_audio_path):
                os.remove(processed_audio_path)

            return gen_wav[0, 0, :].cpu().numpy()

        except Exception as e:
            logger.error(f"Error generating speech: {e}")
            raise
    
    def save_audio(self, audio: np.ndarray, output_path: str) -> str:
        """
        Save audio array to file.

        Args:
            audio: Audio array to save
            output_path: Output file path

        Returns:
            Path to saved file
        """
        try:
            sf.write(output_path, audio, self.sample_rate)
            return output_path
        except Exception as e:
            logger.error(f"Error saving audio: {e}")
            raise

    def generate_and_save(
        self,
        sample_audio_path: str,
        target_text: str,
        output_path: Optional[str] = None,
        prompt_text: Optional[str] = None
    ) -> str:
        """
        Generate speech and save to file.

        Args:
            sample_audio_path: Path to voice sample for cloning
            target_text: Text to synthesize
            output_path: Output file path (optional)
            prompt_text: Optional prompt text

        Returns:
            Path to generated audio file
        """
        if output_path is None:
            output_path = tempfile.mktemp(suffix=".wav")

        audio = self.text_to_speech(sample_audio_path, target_text, prompt_text)
        return self.save_audio(audio, output_path)
